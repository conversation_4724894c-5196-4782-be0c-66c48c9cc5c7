# fly.toml app configuration file generated for realfluence on 2025-07-25T21:48:21+05:30
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'realfluence'
primary_region = 'bom'

[build]

[env]
  NODE_ENV = 'production'
  PORT = '3000'
  S3_BUCKET = 'testtrustpeer'
  S3_REGION = 'us-east-1'
  SCOPES = 'read_customers,read_discounts,read_orders,write_discounts,write_products'
  SHOPIFY_API_KEY = '5e08ea721cf5671aa6f3efc696843904'
  SHOPIFY_APP_URL = 'https://realfluence.fly.dev'

[[mounts]]
  source = 'data'
  destination = '/data'
  auto_extend_size_threshold = 80
  auto_extend_size_increment = '1GB'
  auto_extend_size_limit = '10GB'

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
